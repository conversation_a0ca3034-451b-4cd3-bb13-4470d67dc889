#pragma comment(lib,"ws2_32.lib")
#pragma warning(disable:4996) 
#include<stdio.h>
#include<stdlib.h> 
#include<string.h>
#include <winsock2.h>
#include <time.h>
#include<windows.h>

/*sockaddr_in ��internet�������׽��ֵĵ�ַ��ʽ��
�����������������ǻ��sockaddr_in�ṹ����в�����ʹ��sockaddr_in�������������Ϣ��
���ʹ������ת���Ϳ����ˡ�
һ���Ȱ�sockaddr_in������ֵ��ǿ������ת��������sockaddr�������ĺ���
sockaddr_in����socket����͸�ֵ��sockaddr���ں���������*/



typedef struct url_ip
{
	char IP[16];
	char name[BUFSIZ];
}url_ip;			// url_ip�Ǳ��ش��������ip��Ӧ�������ݽṹ 
url_ip Url_ip[1024];
int Url_ip_size = 0;

typedef struct header
{
	unsigned short ID;
	unsigned short QR_RCODE;
	unsigned short QDCOUNT;
	unsigned short ANCOUNT;
	unsigned short NSCOUNT;
	unsigned short ARCOUNT;
}Header;

typedef struct Question
{
	unsigned char *QNAME; // ??? malloc ����˭������ôд�ģ�
	unsigned short QTYPE;
	unsigned short QCLASS;
} Question;

typedef struct ResourceRecord
{
	unsigned short NAME;//һ��ΪC00C
	/*�ʼ������bit���붼Ϊ1��Ŀ����Ϊ�˱��������
	�����14bit��ʾ�ַ���������DNS��Ϣ���е�ƫ����������DNSӦ����е�Answers�γ��ֵ�����������Queries���Ѿ����֣���˺���ֻ��ʹ����ƫ������ʾ���ɡ�
	��Ȼ��Queries�е��������ֵ�Ƶ����ߣ������е�һ�����ֵ�����ƫ�����̶�Ϊ12�ֽڣ�00001100���������ʼ������1*/
	unsigned short TYPE;
	unsigned short CLASS;
	unsigned int  TTL;
	unsigned short RDLENGTH;
	unsigned char* RDATA;
} ResourceRecord;

struct Message
{
	Header dnsheader;
	Question* question;
	ResourceRecord* answer;
	ResourceRecord* authority;
	ResourceRecord* additional;
	SOCKADDR_IN senderAddr;
} message;

typedef struct ID_Trans_Unit 
{
	unsigned short ID;
	unsigned short CurrentId;
	SOCKADDR_IN senderAddr;
	int used;//1��ʾʹ�ù���0��ʾδʹ��
} ID_Trans_Unit;

struct RecordTable {
	ID_Trans_Unit recordtable[1000];
	int size;
} recordTable;

unsigned short true_CurrentId = 0;

typedef struct Node
{
	char IP[16];
	char url[65];
	struct Node* next;
	struct Node* prior;
}Node;

struct CACHE
{
	Node* head;
	int size;
	Node* tail;
}CACHE;

struct LRU
{
	Node* head;
	int size;
	Node* tail;
}LRU;

int debug_level;
char IP_address[16];//ip��ַ 
char DNS_address[100] = "dnsrelay.txt";//����·�� 


void init();
void get_parameter(int argc, char* argv[]);
void read_local_file();
void create_socket();



struct HandleExternMessageParams {
	struct sockaddr_in* clientAddr;
	char* msg;
	int len;
};

struct HandleMsgParams {
	int len;
	char* msg;
	struct sockaddr_in* clientAddr;
};

void tryToGetMessageFromClient();
void handleGetMessageFromClient(struct HandleMsgParams* params);
void sendDnsRequestToExtern(struct sockaddr_in* clientAddr, char* msg, int len);
void handleGetMessageFromExtern(SOCKET* tempSocket, struct sockaddr_in* tempExternAddr, SOCKET* serverSocket, struct sockaddr_in* clientAddr, char* msg, int len);