#include"main.h"

SOCKET local_sock;
struct sockaddr_in local_addr, extern_addr;//AF_INET???
HANDLE hMutex;

WSADATA wsaData;

int main(int argc, char* argv[])//????????????????????./a.out???????????????????????????????????????????????????./a.out???????��???
								//??????????????????????????main???????��??????????????
{
	init();
	get_parameter(argc, argv);
	read_local_file();
	create_socket();

	while (1)
	{
		tryToGetMessageFromClient();
	}
	//????? 
	CloseHandle(hMutex);
	return 0;
}

char* Convert_to_Url(char* qname)
{
	char* url = (unsigned char*)malloc(BUFSIZ);
	if (url) {
		const char* s = qname;
		int current_index = 0;
		unsigned char len;
		while ((len = *s++) != 0x00) {
			while (len--)
				url[current_index++] = *s++;
			url[current_index++] = '.';
		}
		url[--current_index] = '\0';
		printf("?????��?QNAME??%s\n", qname);
		printf("??????URL%s\n", url);
		return url;
	}
	return NULL;
}

void init()
{
	printf("Usage: dnsrelay [-d | -dd] [<dns-server>] [<db-file>]\n");
	//?????????????????"Open Mutex error!"
	if ((hMutex = OpenMutex(MUTEX_ALL_ACCESS, FALSE, "Mutex.Test")) == NULL) {
		printf("Open Mutex error!\n");
	}
}

void get_parameter(int argc, char* argv[])
{
	debug_level = 0;
	switch (argc) {
	case 1: {
		printf("Name level ************.\n");
		printf("Debug level %d.\n", debug_level);
		printf("Try to load local table \"dnsrelay.txt\"\n");
		break;
	}
	case 2: {
		if (argv[1][0] == '-' && argv[1][1] == 'd') {
			debug_level++;

			if (argv[1][2] == 'd') {
				debug_level++;
				printf("Name level ************.\n");
				printf("Debug level %d.\n", debug_level);
				printf("Fail to bind UDP port, Error.");

			}
			else {
				printf("Name level ************.\n");
				printf("Debug level %d.\n", debug_level);
				printf("Fail to bind UDP port, Error.");
			}

		}

		break;
	}
	case 3: {
		if (argv[1][0] == '-' && argv[1][1] == 'd') {
			debug_level++;
			if (argv[1][2] == 'd') {
				debug_level++;
				printf("Name level ************.\n");
				printf("Debug level %d.\n", debug_level);
				printf("Bind UDP port OK!");
				printf("Try to load local table \"dnsrelay.txt\" ... OK\n");
				strcpy(IP_address, argv[2]);
			}
			else {
				printf("Name level ************.\n");
				printf("Debug level %d.\n", debug_level);
				//		printf("Fail to bind UDP port, Error.");
			}

		}
		break;
	}
	case 4: {
		if (argv[1][0] == '-' && argv[1][1] == 'd') {
			debug_level++;
			if (argv[1][2] == 'd') {
				debug_level++;
				printf("Name level ************.\n");
				printf("Debug level %d.\n", debug_level);
				//		printf("Fail to bind UDP port, Error.");


			}
			else {
				printf("Name level ************.\n");
				printf("Debug level %d.\n", debug_level);
				strcpy(IP_address, argv[2]);
				strcpy(DNS_address, argv[3]);
			}

		}
		break;
	}
	}
}

/* Read info from local data */
void read_local_file()
{
	FILE* file;
	if ((file = fopen(DNS_address, "r")) == NULL) /* No such file then return */
	{
		printf("??DNS_address???\n");
		return;
	}
	char url[BUFSIZ], ip[16];		//??�D?��??
	while (fscanf(file, "%s %s", ip, url) > 0) //??????????(format)??????????(stream)?��???????
	{
		if (debug_level >= 1)
			printf("Read from 'dnsrelay.txt' -> [Url : %s, IP : %s]\n", url, ip);
		memcpy(Url_ip[Url_ip_size].IP, ip, sizeof(ip));
		memcpy(Url_ip[Url_ip_size].name, url, sizeof(url));
		Url_ip_size++;
	}
	fclose(file);
}

/**
* ??????????DNS?????????????????
*/
void handleGetMessageFromExtern(SOCKET* tempSocket, struct sockaddr_in* tempExternAddr, SOCKET* serverSocket, struct sockaddr_in* clientAddr, char* msg, int len) {
	//while (1) {
		int addrLen = sizeof(*(tempExternAddr));
		int lenOfExtern = recvfrom(*tempSocket, msg, BUFSIZ * 2, 0, (struct sockaddr*)(tempExternAddr), &addrLen);
		if (lenOfExtern < 0) {
			printf("??????????????????%d\n", GetLastError());
			return;
		}
		else {
 			sendto(local_sock, msg, lenOfExtern, 0, (struct sockaddr*)(clientAddr), sizeof(*(clientAddr)));
			printf("???????????DNS?????\n");
			free(msg);
			//break;
		}
	//}
}

/**
* ???????????????DNS???????????????DNS?????????????_beginthread?????
*/
void sendDnsRequestToExtern(struct sockaddr_in* clientAddr, char* msg, int len) {
	SOCKET tempExternSocket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);//???????????
	if (tempExternSocket < 0)
	{
		if (debug_level >= 1)
			printf("????????????????????DNS???????????\n");
		exit(1);
	}

	struct sockaddr_in  tempExternAddr;
	tempExternAddr.sin_family = AF_INET;                         /* Set the family as AF_INET (TCP/IP) */
	tempExternAddr.sin_port = htons(53);
	tempExternAddr.sin_addr.S_un.S_addr = inet_addr("*******");	//?????????????????????????????????????????

	int addrLen = sizeof(tempExternAddr);
	// ????????????????
	if (sendto(tempExternSocket, msg, len, 0, (struct sockaddr*)&tempExternAddr, addrLen) < 0) {
		printf("ERROR SENDING %d\n", GetLastError());
	}
	// ?????????
	handleGetMessageFromExtern(&tempExternSocket, &tempExternAddr, &local_sock, clientAddr, msg, len);
}

/*
* ????????DNS Server Socket
*/
void create_socket()
{
	if (WSAStartup(MAKEWORD(2, 2), &wsaData))//??WSA(Windows Sockets Asynchronous??Windows???????)??????????,????0?????
	{										//?????was
		printf("WSAStartup ????%d\n", GetLastError());
		exit(1);	//exit?C++?????????exit(1)??exit(-1)

			/*?????1?? - 1??????????
			exit(0)???????0??
			exit(0)??????????????????0????????????*/
	}

	/*	socket?????????
	*
		????????????????????????????????????
		?????????
		????????AF_UNIX
		?????????
		????????AF_INET

		SOCK_DGRAM?????????UDP???????????????????????TCP??????????SOCK_STREAM

		0????????????��???


		int ioctlsocket( int s, long cmd, u_long * argp);
		s????????????????????
		cmd????????s?????????
		argp?????cmd?????????????????

		FIONBIO ????????
		???????????????????
		?????????????????????????????????��?????????????

		argp?????????????????????????????????????????????????????


		*/
	local_sock = socket(AF_INET, SOCK_DGRAM, 0);//?????????????  
	//printf("%d %d\n", local_sock, extern_sock);
	/*Set socket interface to non-blocking mode */
	//int non_block = 1;
	//ioctlsocket(extern_sock, FIONBIO, (u_long FAR*) & non_block);
	//ioctlsocket(local_sock, FIONBIO, (u_long FAR*) & non_block);

	if (local_sock < 0)
	{
		if (debug_level >= 1)
			printf("????????DNS Server Socket????????\n");
		exit(1);
	}


	local_addr.sin_family = AF_INET;            /* Set the family as AF_INET (TCP/IP)??AF_INET????IPV4internet protocols */
	local_addr.sin_addr.s_addr = INADDR_ANY;    /* Set to any ?????????????53???*/
	local_addr.sin_port = htons(53);           /* Set the port as DNS port (53)??dns?????? */
												//htons?????????????????????????????????????????
											 //????????????TCP/IP?����?????????????????????????CPU?????????????????
											//?????????????????????????????????????????????????????big-endian?????

	extern_addr.sin_family = AF_INET;                         /* Set the family as AF_INET (TCP/IP) */
	extern_addr.sin_addr.s_addr = inet_addr("*******");	//?????????????????????????????????????????
	extern_addr.sin_port = htons(53);

	// Server Socket????53????
	if (bind(local_sock, (struct sockaddr*)&local_addr, sizeof(local_addr)))//??????????????????????
	{
		printf("Bind Port 53 ???. %d\n", GetLastError());
		exit(1);
	}
}


//???cache???��?????url?????ip?????????i???��??????????????i
int find_in_cache(unsigned char* url, unsigned char* ip)
{
	int i;
	Node* tempptr = CACHE.head;
	for (i = 0; i < CACHE.size; i++)
	{
		if (!strcmp(tempptr->url, url))
		{
			strcpy(tempptr->IP, ip);
			return i;
		}
		tempptr = tempptr->next;
	}
	return i;
}

//???cache???��?????????ip???
char* getIpStringOfCache(Node* ptr)
{
	if (ptr == NULL)
	{
		printf("?????Cahce?????????");
		return NULL;
	}
	return ptr->IP;
}

//???cache???��?????????i
Node* getCacheByUrl(unsigned char* url)
{
	Node* tempptr = CACHE.head;
	for (int i = 0; i < CACHE.size; i++)
	{
		if (!strcmp(tempptr->url, url))
		{
			return tempptr;
		}
		tempptr = tempptr->next;
	}
	return NULL;
}

//????LRU???????????url?????????????
char* getLastItemOfLRU()//???LRU?????????????
{
	return LRU.tail;
}

//??LRU?????url?????????
void deleteNodeFromLRU(Node* node)
{
	if (node->prior) {
		Node* pri = node->prior;
		pri->next = node->next;
	}
	else {
		Node* next = node->next;
		LRU.head = next;
	}
	if (node->next) {
		Node* next = node->next;
		next->prior = node->prior;
	}
	else {
		Node* pri = node->prior;
		LRU.tail = pri;
	}
	LRU.size--;
	free(node);
}

//??Cache?????url?????????
void deleteNodeFromCache(Node* node)
{
	if (node->prior) {
		Node* pri = node->prior;
		pri->next = node->next;
	}
	else {
		Node* next = node->next;
		CACHE.head = next;
	}
	if (node->next) {
		Node* next = node->next;
		next->prior = node->prior;
	}
	else {
		Node* pri = node->prior;
		CACHE.tail = pri;
	}
	CACHE.size--;
	free(node);
}

void addNewCacheAndLRU(unsigned char* url, unsigned char* ip)
{
	//?????????? 
	WaitForSingleObject(hMutex, INFINITE);
	if (LRU.size >= 5) {
		deleteNodeFromLRU(LRU.tail);
		deleteNodeFromCache(CACHE.head);
	}
	// cache��??
	Node* node = (Node*)malloc(sizeof(Node));
	if (node) {
		node->next = NULL;
		node->prior = NULL;
		if (node != NULL) {
			strcpy(node->url, url);
			strcpy(node->IP, ip);
			if (CACHE.head == NULL) {
				CACHE.head = CACHE.tail = node;
			}
			else {
				CACHE.tail->next = node;
				node->prior = CACHE.tail;
				CACHE.tail = node;
			}
			CACHE.size++;
		}
	}

	// LRU???
	node = (Node*)malloc(sizeof(Node));
	if (node) {
		node->next = NULL;
		node->prior = NULL;
		if (node != NULL) {
			strcpy(node->url, url);
			strcpy(node->IP, ip);
			if (LRU.head == NULL) {
				LRU.head = LRU.tail = node;
			}
			else {
				node->next = LRU.head;
				LRU.head->prior = node;
				LRU.head = node;
			}
			LRU.size++;
		}
	}
	//???????? 
	ReleaseMutex(hMutex);
}

void output_cache()
{
	//?????????? 
	WaitForSingleObject(hMutex, INFINITE);
	printf("-----CACHE-----\n");
	Node* curptr = CACHE.head;
	for (int i = 0; i < CACHE.size; i++)
	{
		printf("%d  url:%s  ip:%s\n", i, curptr->url, curptr->IP);
		curptr = curptr->next;
	}
	//???????? 
	ReleaseMutex(hMutex);
}

void output_LRU()
{
	//?????????? 
	WaitForSingleObject(hMutex, INFINITE);
	printf("-----LRU-----\n");
	Node* curptr = LRU.head;
	for (int i = 0; i < LRU.size; i++)
	{
		printf("%d  url:%s  ip:%s\n", i, curptr->url, curptr->IP);
		curptr = curptr->next;
	}
	//???????? 
	ReleaseMutex(hMutex);
}


//??LRU??????????
void addRecordToLru(unsigned char* url, unsigned char* ip)
{
	addNewCacheAndLRU(url, ip);
	output_cache();
	output_LRU();
}

//???????????????????DNS?????��????????��?????????i???????????????i
int find_in_Url_ip(char* url)
{
	int i;
	for (i = 0; i < Url_ip_size; i++)
	{
		char* a = url;
		char* b = Url_ip[i].name;
		if (!strcmp(Url_ip[i].name, url))
			return i;
	}
	return -1;
}

void LRU_push_front(Node *node)
{
	if (node->prior) {
		Node* pri = node->prior;
		if (pri) {
			pri->next = node->next;
		}
	}
	if (node->next) {
		Node* next = node->next;
		if (next) {
			next->prior = node->prior;
		}
	}
	Node* head = LRU.head;
	LRU.head = node;
	if (node != head) {
		node->next = head;
		if (head) {
			head->prior = node;
		}
	}
}

void freeSpace()
{
	int i;
	if (message.question) {
		for (i = 0; i < message.dnsheader.QDCOUNT; i++) {
			if (message.question[i].QNAME)
				free(message.question[i].QNAME);
		}
		free(message.question);
	}

	if (message.answer) {
		for (i = 0; i < message.dnsheader.ANCOUNT; i++) {
			if (message.answer[i].NAME)
				free(message.answer[i].NAME);
			if (message.answer[i].RDATA)
				free(message.answer[i].RDATA);
		}
		free(message.answer);
	}

	if (message.authority) {
		for (i = 0; i < message.dnsheader.NSCOUNT; i++) {
			if (message.authority[i].NAME)
				free(message.authority[i].NAME);
			if (message.authority[i].RDATA)
				free(message.authority[i].RDATA);
		}
		free(message.authority);
	}

	if (message.additional) {
		for (i = 0; i < message.dnsheader.ARCOUNT; i++) {
			if (message.additional[i].NAME)
				free(message.additional[i].NAME);
			if (message.additional[i].RDATA)
				free(message.additional[i].RDATA);
		}
		free(message.additional);
	}
}

void explain_message(char* buf, SOCKADDR_IN senderAddr)
{
	unsigned char* buffer = buf;
	//header
	message.dnsheader.ID = ntohs(*(unsigned short*)buffer);
	buffer += sizeof(unsigned short);
	message.dnsheader.QR_RCODE = ntohs(*(unsigned short*)buffer);
	buffer += sizeof(unsigned short);
	message.dnsheader.QDCOUNT = ntohs(*(unsigned short*)buffer);
	buffer += sizeof(unsigned short);
	message.dnsheader.ANCOUNT = ntohs(*(unsigned short*)buffer);
	buffer += sizeof(unsigned short);
	message.dnsheader.NSCOUNT = ntohs(*(unsigned short*)buffer);
	buffer += sizeof(unsigned short);
	message.dnsheader.ARCOUNT = ntohs(*(unsigned short*)buffer);
	buffer += sizeof(unsigned short);
	printf("message_header??????\n");

	int i;
	//question
	message.question = (Question*)malloc(sizeof(Question) * message.dnsheader.QDCOUNT);
	printf("message.QDCOUNT %d\n", message.dnsheader.QDCOUNT);
	for (i = 0; i < message.dnsheader.QDCOUNT; i++)
	{
		printf("length of qname: %d\n", strlen(buffer));

		message.question[i].QNAME = (unsigned char*)malloc(BUFSIZ);
		strcpy(message.question[i].QNAME, buffer);
		printf("copyed successfully\n");
		char* tmp = Convert_to_Url(message.question[i].QNAME);
		free(message.question[i].QNAME);
		message.question[i].QNAME = tmp;

		buffer += strlen(buffer) + 1;

		message.question[i].QTYPE = ntohs(*(unsigned short*)buffer);
		buffer += sizeof(unsigned short);

		message.question[i].QCLASS = ntohs(*(unsigned short*)buffer);
		buffer += sizeof(unsigned short);
	}
	printf("message_question??????\n");


	//authority
	message.authority = (ResourceRecord*)malloc(sizeof(ResourceRecord) * message.dnsheader.NSCOUNT);
	for (i = 0; i < message.dnsheader.NSCOUNT; i++)
	{
		buffer += sizeof(unsigned short);

		message.authority[i].TYPE = ntohs(*((unsigned short*)buffer));
		buffer += sizeof(unsigned short);
		message.authority[i].CLASS = ntohs(*((unsigned short*)buffer));
		buffer += sizeof(unsigned short);
		message.authority[i].TTL = ntohl(*((unsigned long*)buffer));
		buffer += sizeof(unsigned long);
		message.authority[i].RDLENGTH = ntohs(*((unsigned short*)buffer));
		buffer += sizeof(unsigned short);
		message.authority[i].RDATA = (unsigned char*)malloc(message.authority[i].RDLENGTH);
		memcpy(message.authority[i].RDATA, buffer, message.authority[i].RDLENGTH);
		buffer += message.authority[i].RDLENGTH;
	}
	printf("message_authority??????\n");

	//additional
	message.additional = (ResourceRecord*)malloc(sizeof(ResourceRecord) * message.dnsheader.ARCOUNT);
	for (i = 0; i < message.dnsheader.ARCOUNT; i++)
	{
		buffer += sizeof(unsigned short);

		message.additional[i].TYPE = ntohs(*((unsigned short*)buffer));
		buffer += sizeof(unsigned short);
		message.additional[i].CLASS = ntohs(*((unsigned short*)buffer));
		buffer += sizeof(unsigned short);
		message.additional[i].TTL = ntohl(*((unsigned long*)buffer));
		buffer += sizeof(unsigned long);
		message.additional[i].RDLENGTH = ntohs(*((unsigned short*)buffer));
		buffer += sizeof(unsigned short);
		message.additional[i].RDATA = (unsigned char*)malloc(message.additional[i].RDLENGTH);
		memcpy(message.additional[i].RDATA, buffer, message.additional[i].RDLENGTH);
		buffer += message.additional[i].RDLENGTH;
	}
	printf("message_additional??????\n");
	message.senderAddr = senderAddr;
	printf("message resolved\n");
}


void handleGetMessageFromClient(struct HandleMsgParams* params) {
	printf("length: %d\n", params->len);
	if (params->len == -1)
	{
		printf("????DNS?????????????????????? %d\n", GetLastError());
		return;//?????????????????????��??????????????????-1
	}
	//freeSpace();
	printf("link list freed\n");

	explain_message(params->msg, local_addr);


	if (debug_level)
	{
		/* Output time now */
		time_t t = time(NULL);
		char temp[64];
		strftime(temp, sizeof(temp), "%Y/%m/%d %X %A", localtime(&t));
		printf("\n\n---- Recv : Client [IP:%s]----\n", inet_ntoa(params->clientAddr->sin_addr));
		printf("%s\n", temp);
		printf("Receive from client [Query : %s]\n", message.question->QNAME);
	}

	int i = -1;
	Node* j = NULL;
	(j = getCacheByUrl(message.question->QNAME)) != NULL || (i = find_in_Url_ip(message.question->QNAME)) != -1;
	unsigned short opcode = (message.dnsheader.QR_RCODE & 0x7800) >> 11;
	if (message.question[0].QTYPE == 0x01 && (i != -1 || j != NULL) && opcode == 0)
	{
		char ip[16];//????????/cache???????IP???
		// ??cache??
		if (j != NULL)
		{
			strcpy(ip, getIpStringOfCache(j));
			LRU_push_front(j);
			if (debug_level >= 1)
				printf("??????????Read from cache [Url:%s -> IP:%s]\n", message.question->QNAME, ip);
			output_cache();
			output_LRU();
		}
		// ???????????
		else if (i != -1)
		{
			strcpy(ip, Url_ip[i].IP);
			if (debug_level >= 1)
				printf("??????????Read from Url_ip [Url:%s -> IP:%s]\n", message.question->QNAME, ip);
			addRecordToLru(message.question->QNAME, ip);
		}

		char sendbuf[1024];
		memcpy(sendbuf, params->msg, params->len);
		unsigned short head_second, head_fourth;//????header??????(QR??RCODE)???????(ANCOUNT)
		if (!strcmp(ip, "0.0.0.0"))
		{
			head_second = htons(0x8183);//RCODE??3???????????????????
			memcpy(&sendbuf[2], &head_second, sizeof(unsigned short));
			head_fourth = htons(0x0000);//ANCOUNT=0
			memcpy(&sendbuf[6], &head_fourth, sizeof(unsigned short));
		}
		else
		{
			head_second = htons(0x8180);//RCODE??0???????
			unsigned short zero = htons(0x0);
			unsigned short one = htons(0x1);
			head_fourth = htons(0x0001);//ANCOUNT=1
			// ??????
			memcpy(&sendbuf[2], &head_second, sizeof(unsigned short));
			// ????????1
			memcpy(&sendbuf[4], &one, sizeof(unsigned short));
			// ???????1
			memcpy(&sendbuf[6], &head_fourth, sizeof(unsigned short));
			// ???????
			memcpy(&sendbuf[8], &zero, sizeof(unsigned short));
			// ????????
			memcpy(&sendbuf[10], &zero, sizeof(unsigned short));
		}

		char* QName = &sendbuf[12];

		int qnameLength = strlen(QName);

		char* answerBase = QName + qnameLength + 1 + 4;

		int offset = answerBase - sendbuf;

		// name
		unsigned short nameTmp = htons(0xC00C);
		memcpy(answerBase, &nameTmp, 2);
		// type
		unsigned short typeTmp = htons(0x0001);
		memcpy(answerBase + 2, &typeTmp, 2);
		// class
		unsigned short classTmp = htons(0x0001);
		memcpy(answerBase + 4, &classTmp, 2);
		// ttl
		unsigned int ttlTmp = htonl(0x0);
		memcpy(answerBase + 6, &ttlTmp, 4);
		// len
		unsigned short lenTmp = htons(0x04);
		memcpy(answerBase + 10, &lenTmp, 2);
		// data
		unsigned int tmp_ip = (unsigned int)inet_addr(ip);
		memcpy(answerBase + 12, &tmp_ip, 4);

		int length = sendto(local_sock, sendbuf, 28 + qnameLength + 1 + 4, 0, (SOCKADDR*)params->clientAddr, sizeof(struct sockaddr_in));
		if (length < 0)
			printf("Error :(???????) Send packet -> length < 0\n");
	}
	else//?��??????????
	{
		sendDnsRequestToExtern(params->clientAddr, params->msg, params->len);
	}
}

/*
* ???UDP????
*/
void tryToGetMessageFromClient()
{
	struct sockaddr_in* clientAddr = (struct sockaddr_in*)malloc(sizeof(struct sockaddr_in));
	char* buf = (char*)malloc(BUFSIZ * 2);
	memset(buf, 0, 1024);
	int length = -1;
	int client_length = sizeof(struct sockaddr_in);
	length = recvfrom(local_sock, buf, BUFSIZ * 2, 0, (struct sockaddr*)clientAddr, &client_length);/* Receive packet from client */
	struct HandleMsgParams* params = (struct HandleMsgParams*)malloc(sizeof(struct HandleMsgParams));
	if (buf == NULL || clientAddr == NULL || params == NULL) {
		printf("????Client UDP?????????????????");
		return;
	}
	params->clientAddr = clientAddr;
	params->len = length;
	params->msg = buf;
	// ???????
	_beginthread(handleGetMessageFromClient, 0, params);
}



